import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { title, description, priority, assigneeId, targets } = await request.json();

    // Validate required fields
    if (!title || typeof title !== 'string' || title.trim() === '') {
        return NextResponse.json({ error: 'Title is required' }, { status: 400 });
    }

    // Validate priority if provided
    const validPriorities = ['low', 'medium', 'high', 'urgent'];
    if (priority && !validPriorities.includes(priority)) {
        return NextResponse.json({ error: 'Invalid priority value' }, { status: 400 });
    }

    // Validate targets if provided
    if (targets && (!Array.isArray(targets) || targets.some(t => !t.target_type || !t.target_id))) {
        return NextResponse.json({ error: 'Invalid targets format' }, { status: 400 });
    }

    try {
        // Create the ticket
        const { data: ticketData, error: ticketError } = await supabase
            .from('tickets')
            .insert({
                creator_id: userId,
                assignee_id: assigneeId || null,
                title: title.trim(),
                description: description || null,
                priority: priority || 'medium',
                status: 'open',
                updated_by: userId
            })
            .select()
            .single();

        if (ticketError) {
            return NextResponse.json({ error: ticketError.message }, { status: 500 });
        }

        // Create ticket targets if provided
        if (targets && targets.length > 0) {
            const targetInserts = targets.map(target => ({
                ticket_id: ticketData.id,
                target_type: target.target_type,
                target_id: target.target_id
            }));

            const { error: targetsError } = await supabase
                .from('ticket_targets')
                .insert(targetInserts);

            if (targetsError) {
                // Log error but don't fail the ticket creation
                console.error('Error creating ticket targets:', targetsError);
            }
        }

        return NextResponse.json({ success: true, data: ticketData });
    } catch (error) {
        console.error('Ticket creation error:', error);
        return NextResponse.json({ error: 'Failed to create ticket' }, { status: 500 });
    }
});

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);

    const status = searchParams.get('status');
    const myTickets = searchParams.get('myTickets') === 'true';

    try {
        let query = supabase
            .from('tickets')
            .select(`
                *,
                ticket_targets(
                    id,
                    target_type,
                    target_id
                )
            `)
            .order('created_at', { ascending: false });

        // Filter by user involvement (creator or assignee)
        if (myTickets) {
            query = query.or(`creator_id.eq.${userId},assignee_id.eq.${userId}`);
        } else {
            // For non-myTickets, still filter by user access (RLS will handle this too)
            query = query.or(`creator_id.eq.${userId},assignee_id.eq.${userId}`);
        }

        // Filter by status if provided
        if (status) {
            query = query.eq('status', status);
        }

        const { data: tickets, error } = await query;

        if (error) {
            console.error('Supabase query error:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Fetch user information separately for creator and assignee
        if (tickets && tickets.length > 0) {
            const userIds = new Set<string>();
            tickets.forEach(ticket => {
                if (ticket.creator_id) userIds.add(ticket.creator_id);
                if (ticket.assignee_id) userIds.add(ticket.assignee_id);
            });

            const { data: users, error: usersError } = await supabase.auth.admin.listUsers();

            if (!usersError && users) {
                const userMap = new Map();
                users.users.forEach(user => {
                    userMap.set(user.id, { id: user.id, email: user.email });
                });

                // Add user information to tickets
                tickets.forEach(ticket => {
                    ticket.creator = userMap.get(ticket.creator_id) || null;
                    ticket.assignee = userMap.get(ticket.assignee_id) || null;
                });
            }
        }

        console.log('Query result:', { count: tickets?.length, data: tickets });
        return NextResponse.json({ success: true, data: tickets });
    } catch (error) {
        console.error('Tickets fetch error:', error);
        return NextResponse.json({ error: 'Failed to fetch tickets' }, { status: 500 });
    }
});
