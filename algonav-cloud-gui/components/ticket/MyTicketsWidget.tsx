'use client';

import React from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  Chip,
  Button,
  Divider,
  Stack,
  CircularProgress,
  Alert
} from '@mui/material';
import Link from 'next/link';
import ConfirmationNumberIcon from '@mui/icons-material/ConfirmationNumber';
import { formatDistanceToNow } from 'date-fns';

interface Ticket {
  id: string;
  title: string;
  status: 'open' | 'in_progress' | 'waiting_on_customer' | 'resolved' | 'closed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  created_at: string;
  updated_at: string;
}

interface MyTicketsWidgetProps {
  tickets: Ticket[];
  loading?: boolean;
  error?: string | null;
  onTicketClick?: (ticket: Ticket) => void;
}

const statusColors = {
  open: 'warning',
  waiting_on_customer: 'info',
  resolved: 'success',
  closed: 'default',
  cancelled: 'error'
} as const;

const priorityColors = {
  low: 'default',
  medium: 'primary',
  high: 'warning',
  urgent: 'error'
} as const;

const statusLabels = {
  open: 'Open',
  waiting_on_customer: 'Waiting',
  resolved: 'Resolved',
  closed: 'Closed',
  cancelled: 'Cancelled'
};

const priorityLabels = {
  low: 'Low',
  medium: 'Medium',
  high: 'High',
  urgent: 'Urgent'
};

export function MyTicketsWidget({ 
  tickets, 
  loading = false, 
  error = null,
  onTicketClick 
}: MyTicketsWidgetProps) {
  // Get counts by status
  const statusCounts = tickets.reduce((acc, ticket) => {
    acc[ticket.status] = (acc[ticket.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const openTicketsCount = (statusCounts.open || 0) + (statusCounts.waiting_on_customer || 0);
  const totalTicketsCount = tickets.length;

  // Get recent tickets (last 5)
  const recentTickets = tickets
    .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
    .slice(0, 5);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Summary Stats */}
      <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h4" color="primary" fontWeight="bold">
            {openTicketsCount}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Open Tickets
          </Typography>
        </Box>
        <Divider orientation="vertical" flexItem />
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h4" color="text.secondary" fontWeight="bold">
            {totalTicketsCount}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Total Tickets
          </Typography>
        </Box>
      </Stack>

      <Divider sx={{ mb: 2 }} />

      {/* Recent Tickets List */}
      <Typography variant="subtitle2" gutterBottom>
        Recent Tickets
      </Typography>

      {recentTickets.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 3 }}>
          <ConfirmationNumberIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }} />
          <Typography variant="body2" color="text.secondary">
            No tickets found
          </Typography>
        </Box>
      ) : (
        <List dense sx={{ p: 0 }}>
          {recentTickets.map((ticket, index) => (
            <React.Fragment key={ticket.id}>
              <ListItem
                sx={{
                  px: 0,
                  cursor: onTicketClick ? 'pointer' : 'default',
                  '&:hover': onTicketClick ? {
                    backgroundColor: 'action.hover',
                    borderRadius: 1
                  } : {}
                }}
                onClick={() => onTicketClick?.(ticket)}
              >
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          flex: 1
                        }}
                      >
                        #{ticket.id} {ticket.title}
                      </Typography>
                      <Stack direction="row" spacing={0.5}>
                        <Chip
                          label={statusLabels[ticket.status]}
                          color={statusColors[ticket.status]}
                          size="small"
                          sx={{ fontSize: '0.7rem', height: 20 }}
                        />
                        {ticket.priority !== 'medium' && (
                          <Chip
                            label={priorityLabels[ticket.priority]}
                            color={priorityColors[ticket.priority]}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        )}
                      </Stack>
                    </Box>
                  }
                  secondary={
                    <Typography variant="caption" color="text.secondary">
                      Updated {formatDistanceToNow(new Date(ticket.updated_at), { addSuffix: true })}
                    </Typography>
                  }
                />
              </ListItem>
              {index < recentTickets.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      )}

      {/* View All Button */}
      {totalTicketsCount > 0 && (
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Link href="/tickets" passHref style={{ textDecoration: 'none' }}>
            <Button variant="outlined" size="small" fullWidth>
              View All Tickets ({totalTicketsCount})
            </Button>
          </Link>
        </Box>
      )}
    </Box>
  );
}
